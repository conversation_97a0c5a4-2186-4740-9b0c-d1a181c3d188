<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>画廊 - 梦羽AI绘图</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .gallery-card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease-in-out;
        }
        .gallery-card:hover {
            transform: translateY(-2px);
        }
        .gallery-image {
            width: 100%;
            height: 300px;
            object-fit: cover;
            border-radius: 5px;
            cursor: pointer;
        }
        .gallery-video {
            width: 100%;
            height: 300px;
            border-radius: 5px;
        }
        .placeholder-image {
            width: 100%;
            height: 300px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            border-radius: 5px;
            font-size: 14px;
        }
        .prompt-text {
            font-size: 0.9em;
            color: #666;
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .negative-prompt-text {
            font-size: 0.8em;
            color: #999;
            max-height: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .user-info {
            font-size: 0.85em;
            color: #007bff;
        }
        .time-info {
            font-size: 0.8em;
            color: #6c757d;
        }
        .model-info {
            font-size: 0.8em;
            color: #28a745;
        }
        .no-records {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        .refresh-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .gallery-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .type-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
        }
        .card-img-container {
            position: relative;
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <a href="/" class="btn btn-primary back-btn">
        <i class="fas fa-arrow-left me-1"></i>返回首页
    </a>
    
    <!-- 刷新按钮 -->
    <button class="btn btn-success refresh-btn" onclick="location.reload()">
        <i class="fas fa-sync-alt me-1"></i>刷新
    </button>

    <div class="container">
        <!-- 页面标题 -->
        <div class="gallery-header">
            <h1><i class="fas fa-images me-2"></i>画廊</h1>
            <p class="text-muted mb-3">展示最近一段时间内所有用户的精彩作品</p>

            <!-- 时间范围选择 -->
            <div class="row justify-content-center mb-3">
                <div class="col-auto">
                    <select id="timeRange" class="form-select form-select-sm">
                        <option value="1" selected>最近1小时</option>
                        <option value="3">最近3小时</option>
                        <option value="6">最近6小时</option>
                        <option value="12">最近12小时</option>
                        <option value="24">最近24小时</option>
                    </select>
                </div>
                <div class="col-auto">
                    <button id="loadGallery" class="btn btn-primary btn-sm">
                        <i class="fas fa-search me-1"></i>查看
                    </button>
                </div>
            </div>

            <small class="text-muted">共 <span id="recordCount">{{ records|length }}</span> 个作品</small>
        </div>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="text-center" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载画廊内容...</p>
        </div>

        <!-- 画廊内容容器 -->
        <div id="galleryContainer">
            {% if records %}
            <div class="row" id="galleryGrid">
                {% for record in records %}
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <div class="card gallery-card">
                        <div class="card-img-container">
                            {% if record.type == 'image' and record.image_url %}
                                <img src="{{ record.image_url }}" class="gallery-image" alt="生成的图片" loading="lazy"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="placeholder-image" style="display: none;">
                                    <div class="text-center">
                                        <i class="fas fa-image fa-2x mb-2"></i><br>
                                        图片加载失败
                                    </div>
                                </div>
                                <span class="badge bg-primary type-badge">图片</span>
                            {% elif record.type == 'video' and record.video_url %}
                                <video class="gallery-video" controls preload="metadata">
                                    <source src="{{ record.video_url }}" type="video/mp4">
                                    您的浏览器不支持视频播放。
                                </video>
                                <span class="badge bg-success type-badge">视频</span>
                            {% else %}
                                <div class="placeholder-image">
                                    <div class="text-center">
                                        <i class="fas fa-question fa-2x mb-2"></i><br>
                                        内容不可用
                                    </div>
                                </div>
                                <span class="badge bg-secondary type-badge">未知</span>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <!-- 用户信息 -->
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="user-info">
                                    <i class="fas fa-user me-1"></i>{{ record.username }}
                                </span>
                                <span class="time-info">
                                    <i class="fas fa-clock me-1"></i>{{ record.timestamp[:19].replace('T', ' ') }}
                                </span>
                            </div>

                            <!-- 模型信息 -->
                            {% if record.model_name %}
                            <div class="model-info mb-2">
                                <i class="fas fa-cog me-1"></i>{{ record.model_name }}
                            </div>
                            {% endif %}

                            <!-- 提示词 -->
                            <div class="mb-2">
                                <strong>提示词：</strong>
                                <div class="prompt-text">{{ record.prompt }}</div>
                            </div>

                            <!-- 负面提示词 -->
                            {% if record.negative_prompt %}
                            <div class="mb-2">
                                <strong>负面提示词：</strong>
                                <div class="negative-prompt-text">{{ record.negative_prompt }}</div>
                            </div>
                            {% endif %}

                            <!-- 操作按钮 -->
                            <div class="d-grid">
                                {% if record.type == 'image' and record.image_url %}
                                    <a href="{{ record.image_url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-external-link-alt me-1"></i>查看原图
                                    </a>
                                {% elif record.type == 'video' and record.video_url %}
                                    <a href="{{ record.video_url }}" target="_blank" class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-external-link-alt me-1"></i>查看原视频
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="no-records">
                <i class="fas fa-images fa-3x mb-3 text-muted"></i>
                <h3>暂无作品</h3>
                <p>最近一小时内还没有用户生成作品，快去<a href="/">首页</a>创作吧！</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">图片预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" class="img-fluid" alt="预览图片">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <a id="modalImageLink" href="#" target="_blank" class="btn btn-primary">查看原图</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 初始化图片预览功能
        function initImagePreview() {
            document.querySelectorAll('.gallery-image').forEach(img => {
                img.addEventListener('click', function() {
                    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
                    const modalImage = document.getElementById('modalImage');
                    const modalLink = document.getElementById('modalImageLink');

                    modalImage.src = this.src;
                    modalLink.href = this.src;
                    modal.show();
                });
            });
        }

        // 格式化时间显示
        function formatTimeDisplay() {
            document.querySelectorAll('.time-info').forEach(timeElement => {
                const timeText = timeElement.textContent.trim();
                const timeMatch = timeText.match(/(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/);
                if (timeMatch) {
                    const timestamp = new Date(timeMatch[1]);
                    const now = new Date();
                    const diff = Math.floor((now - timestamp) / 1000 / 60); // 分钟差

                    let relativeTime;
                    if (diff < 1) {
                        relativeTime = '刚刚';
                    } else if (diff < 60) {
                        relativeTime = `${diff}分钟前`;
                    } else if (diff < 1440) {
                        relativeTime = `${Math.floor(diff / 60)}小时前`;
                    } else {
                        relativeTime = `${Math.floor(diff / 1440)}天前`;
                    }

                    timeElement.innerHTML = `<i class="fas fa-clock me-1"></i>${relativeTime}`;
                    timeElement.title = timeMatch[1]; // 鼠标悬停显示完整时间
                }
            });
        }

        // 加载画廊数据
        async function loadGallery(hours = 1) {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const galleryContainer = document.getElementById('galleryContainer');
            const recordCount = document.getElementById('recordCount');

            try {
                loadingIndicator.style.display = 'block';
                galleryContainer.style.display = 'none';

                const response = await fetch(`/api/gallery?hours=${hours}&limit=100`);
                const data = await response.json();

                if (data.success) {
                    renderGallery(data.records);
                    recordCount.textContent = data.count;
                } else {
                    throw new Error('加载失败');
                }
            } catch (error) {
                console.error('加载画廊数据失败:', error);
                galleryContainer.innerHTML = `
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        加载失败，请稍后重试
                    </div>
                `;
            } finally {
                loadingIndicator.style.display = 'none';
                galleryContainer.style.display = 'block';
            }
        }

        // 渲染画廊内容
        function renderGallery(records) {
            const galleryContainer = document.getElementById('galleryContainer');

            if (records.length === 0) {
                galleryContainer.innerHTML = `
                    <div class="no-records">
                        <i class="fas fa-images fa-3x mb-3 text-muted"></i>
                        <h3>暂无作品</h3>
                        <p>选定时间范围内还没有用户生成作品，快去<a href="/">首页</a>创作吧！</p>
                    </div>
                `;
                return;
            }

            let html = '<div class="row" id="galleryGrid">';
            records.forEach(record => {
                html += renderRecordCard(record);
            });
            html += '</div>';

            galleryContainer.innerHTML = html;

            // 重新初始化功能
            initImagePreview();
            formatTimeDisplay();
        }

        // 渲染单个记录卡片
        function renderRecordCard(record) {
            let mediaHtml = '';
            let badgeHtml = '';
            let buttonHtml = '';

            if (record.type === 'image' && record.image_url) {
                mediaHtml = `
                    <img src="${record.image_url}" class="gallery-image" alt="生成的图片" loading="lazy"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="placeholder-image" style="display: none;">
                        <div class="text-center">
                            <i class="fas fa-image fa-2x mb-2"></i><br>
                            图片加载失败
                        </div>
                    </div>
                `;
                badgeHtml = '<span class="badge bg-primary type-badge">图片</span>';
                buttonHtml = `
                    <a href="${record.image_url}" target="_blank" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>查看原图
                    </a>
                `;
            } else if (record.type === 'video' && record.video_url) {
                mediaHtml = `
                    <video class="gallery-video" controls preload="metadata">
                        <source src="${record.video_url}" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                `;
                badgeHtml = '<span class="badge bg-success type-badge">视频</span>';
                buttonHtml = `
                    <a href="${record.video_url}" target="_blank" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>查看原视频
                    </a>
                `;
            } else {
                mediaHtml = `
                    <div class="placeholder-image">
                        <div class="text-center">
                            <i class="fas fa-question fa-2x mb-2"></i><br>
                            内容不可用
                        </div>
                    </div>
                `;
                badgeHtml = '<span class="badge bg-secondary type-badge">未知</span>';
            }

            const negativePromptHtml = record.negative_prompt ? `
                <div class="mb-2">
                    <strong>负面提示词：</strong>
                    <div class="negative-prompt-text">${record.negative_prompt}</div>
                </div>
            ` : '';

            const modelHtml = record.model_name ? `
                <div class="model-info mb-2">
                    <i class="fas fa-cog me-1"></i>${record.model_name}
                </div>
            ` : '';

            return `
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <div class="card gallery-card">
                        <div class="card-img-container">
                            ${mediaHtml}
                            ${badgeHtml}
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="user-info">
                                    <i class="fas fa-user me-1"></i>${record.username}
                                </span>
                                <span class="time-info">
                                    <i class="fas fa-clock me-1"></i>${record.timestamp.substring(0, 19).replace('T', ' ')}
                                </span>
                            </div>
                            ${modelHtml}
                            <div class="mb-2">
                                <strong>提示词：</strong>
                                <div class="prompt-text">${record.prompt}</div>
                            </div>
                            ${negativePromptHtml}
                            <div class="d-grid">
                                ${buttonHtml}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化现有内容
            initImagePreview();
            formatTimeDisplay();

            // 绑定时间范围选择事件
            document.getElementById('loadGallery').addEventListener('click', function() {
                const hours = document.getElementById('timeRange').value;
                loadGallery(parseInt(hours));
            });

            // 绑定刷新按钮事件
            document.querySelector('.refresh-btn').addEventListener('click', function() {
                const hours = document.getElementById('timeRange').value;
                loadGallery(parseInt(hours));

                // 重置按钮样式
                this.classList.remove('btn-warning');
                this.classList.add('btn-success');
                this.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新';
            });
        });

        // 添加刷新提示
        let lastRefresh = Date.now();
        setInterval(() => {
            const elapsed = Math.floor((Date.now() - lastRefresh) / 1000);
            if (elapsed > 300) { // 5分钟后提示刷新
                const refreshBtn = document.querySelector('.refresh-btn');
                if (refreshBtn && !refreshBtn.classList.contains('btn-warning')) {
                    refreshBtn.classList.remove('btn-success');
                    refreshBtn.classList.add('btn-warning');
                    refreshBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>有新内容';
                }
            }
        }, 30000); // 每30秒检查一次
    </script>
</body>
</html>
